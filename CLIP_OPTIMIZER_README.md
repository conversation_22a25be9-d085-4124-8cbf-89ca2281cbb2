# CLIP Description Optimizer

This script optimizes image descriptions for CLIP (Contrastive Language-Image Pre-training) compatibility using Google Vertex AI's Gemini 2.5 Flash Lite model.

## Features

- **Token Limit Enforcement**: Ensures all descriptions are ≤ 77 tokens (CLIP's hard architectural limit)
- **Intelligent Optimization**: Uses Gemini 2.5 Flash Lite to rewrite descriptions while preserving visual information
- **Retry Logic**: Automatically retries failed optimizations with more aggressive compression
- **Progress Tracking**: Real-time progress updates with token counts and savings
- **Metadata Preservation**: Maintains original JSON structure and adds optimization metadata
- **Comprehensive Statistics**: Detailed reporting on optimization success rates and token savings

## Prerequisites

1. **Google Cloud Project** with Vertex AI API enabled
2. **Authentication** set up for Google Cloud
3. **Python 3.8+** installed

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements_clip_optimizer.txt
```

### 2. Set up Google Cloud Authentication

Choose one of these methods:

**Option A: Service Account Key (Recommended for scripts)**
```bash
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
```

**Option B: gcloud CLI**
```bash
gcloud auth application-default login
```

**Option C: Environment Variable**
```bash
export GOOGLE_CLOUD_PROJECT="your-project-id"
```

### 3. Enable Required APIs

```bash
gcloud services enable aiplatform.googleapis.com
```

## Usage

### Basic Usage

```bash
python clip_optimizer.py
```

The script will:
1. Prompt for your Google Cloud Project ID (if not set in environment)
2. Load the input file: `extracted_ccso_dataset/imagenette_ccso_enhanced_robust_20250821_135956.json`
3. Process each description to optimize for CLIP
4. Save results to: `extracted_ccso_dataset/imagenette_ccso_enhanced_robust_20250821_135956_clip_optimized.json`

### Advanced Usage

You can modify the script to customize:

```python
# In the main() function, modify these variables:
PROJECT_ID = "your-project-id"
INPUT_FILE = "path/to/your/input.json"

# In the CLIPDescriptionOptimizer class, modify:
self.max_tokens = 77  # CLIP's limit
self.max_retries = 3  # Number of retry attempts
self.retry_delay = 2  # Seconds between retries
```

## Input File Format

The script expects a JSON file with this structure:

```json
{
  "classes": {
    "class_name": {
      "descriptions": [
        "Long description that needs optimization...",
        "Another description...",
        ...
      ],
      "metadata": { ... }
    },
    ...
  },
  "processing_info": { ... }
}
```

## Output

The optimized file will have the same structure but with:
- **Optimized descriptions**: All descriptions ≤ 77 tokens
- **Added metadata**: Optimization statistics and timestamp
- **Preserved structure**: All original metadata maintained

### Sample Output Statistics

```
🎉 CLIP OPTIMIZATION COMPLETED!
==================================================
📊 Processing Statistics:
   Total descriptions: 28140
   Successfully optimized: 27985
   Failed optimizations: 155
   Success rate: 99.4%
   Total tokens saved: 145,230
   API calls made: 28,340
   Retries needed: 355
   Processing time: 1,245.6 seconds
```

## Token Counting

The script uses `tiktoken` with the `cl100k_base` encoding, which closely approximates CLIP's tokenization. This ensures accurate token counting for the 77-token limit.

## Error Handling

- **Rate Limiting**: Built-in delays between API calls
- **Retry Logic**: Up to 3 attempts per description with progressive compression
- **Fallback**: Returns original description if optimization fails
- **Validation**: Continuous token count verification

## Cost Estimation

Gemini 2.5 Flash Lite pricing (as of 2024):
- **Input**: ~$0.075 per 1M tokens
- **Output**: ~$0.30 per 1M tokens

For ~28,000 descriptions averaging 100 tokens each:
- Estimated cost: ~$10-15 USD

## Troubleshooting

### Common Issues

1. **Authentication Error**
   ```
   Error: Could not automatically determine credentials
   ```
   **Solution**: Set up Google Cloud authentication (see Setup section)

2. **Project Not Found**
   ```
   Error: Project 'your-project-id' not found
   ```
   **Solution**: Verify project ID and ensure Vertex AI API is enabled

3. **Rate Limiting**
   ```
   Error: Quota exceeded
   ```
   **Solution**: The script includes automatic delays. For heavy usage, consider increasing `retry_delay`

4. **Token Limit Still Exceeded**
   ```
   Warning: Still X tokens (>77)
   ```
   **Solution**: The script will retry with more aggressive compression. Very long descriptions may need manual review.

### Performance Tips

- **Batch Processing**: The script processes descriptions sequentially to avoid rate limits
- **Monitoring**: Watch the console output for real-time progress
- **Interruption**: You can safely interrupt (Ctrl+C) and resume later by modifying the input file

## Validation

After optimization, you can validate the results:

```python
import json
import tiktoken

# Load optimized file
with open('output_file_clip_optimized.json', 'r') as f:
    data = json.load(f)

# Check token counts
tokenizer = tiktoken.get_encoding("cl100k_base")
for class_name, class_data in data['classes'].items():
    for desc in class_data['descriptions']:
        tokens = len(tokenizer.encode(desc))
        if tokens > 77:
            print(f"WARNING: {class_name} has {tokens} tokens: {desc[:50]}...")
```

## License

This script is provided as-is for educational and research purposes. Please ensure compliance with Google Cloud terms of service and usage policies.
