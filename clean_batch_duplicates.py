#!/usr/bin/env python3
"""
Clean Batch File Duplicates
Creates a cleaned copy of batch files with duplicates removed while preserving originals.
"""

import json
import shutil
from pathlib import Path
from collections import defaultdict
from datetime import datetime

def analyze_duplicates(data):
    """Analyze the duplicate patterns in the data."""
    print("🔍 Analyzing duplicate patterns...")
    
    # Group by image filename
    image_groups = defaultdict(list)
    for i, entry in enumerate(data):
        if 'image_filename' in entry:
            image_groups[entry['image_filename']].append(entry)
    
    print(f"📊 Analysis Results:")
    print(f"   Total entries: {len(data)}")
    print(f"   Unique images: {len(image_groups)}")
    
    # Count images with different numbers of descriptions
    count_distribution = defaultdict(int)
    images_with_excess = []
    
    for image_filename, entries in image_groups.items():
        count = len(entries)
        count_distribution[count] += 1

        if count > 3:
            images_with_excess.append((image_filename, count, entries))
    
    print(f"   Distribution of descriptions per image:")
    for count in sorted(count_distribution.keys()):
        print(f"     {count} descriptions: {count_distribution[count]} images")
    
    total_excess = sum((count - 3) * num_images for count, num_images in count_distribution.items() if count > 3)
    print(f"   Total excess descriptions: {total_excess}")
    
    return images_with_excess, count_distribution

def check_for_actual_duplicates(entries):
    """Check if there are actual duplicate descriptions (same text) for an image."""
    descriptions = []
    for entry in entries:
        if 'description' in entry:
            descriptions.append(entry['description'])
    
    # Check for exact duplicates
    unique_descriptions = set(descriptions)
    has_exact_duplicates = len(unique_descriptions) < len(descriptions)
    
    # Check for very similar descriptions (first 100 characters)
    description_prefixes = [desc[:100] for desc in descriptions]
    unique_prefixes = set(description_prefixes)
    has_similar_duplicates = len(unique_prefixes) < len(description_prefixes)
    
    return {
        'total_descriptions': len(descriptions),
        'unique_descriptions': len(unique_descriptions),
        'has_exact_duplicates': has_exact_duplicates,
        'has_similar_duplicates': has_similar_duplicates,
        'descriptions': descriptions
    }

def clean_duplicates_smart(data):
    """Smart duplicate removal that preserves one description per prompt variation."""
    print("🧹 Performing smart duplicate removal...")
    
    # Group by image filename
    image_groups = defaultdict(list)
    for entry in data:
        if 'image_filename' in entry:
            image_groups[entry['image_filename']].append(entry)
    
    cleaned_data = []
    total_removed = 0
    exact_duplicates_found = 0
    
    for image_filename, entries in image_groups.items():
        if len(entries) <= 3:
            # No excess, keep all
            cleaned_data.extend(entries)
        else:
            # Has excess descriptions
            original_count = len(entries)
            
            # Check for actual duplicates
            duplicate_info = check_for_actual_duplicates(entries)
            
            if duplicate_info['has_exact_duplicates']:
                exact_duplicates_found += 1
                print(f"   🔍 Found exact duplicates in {image_filename}")
            
            # Strategy: Keep one description per prompt variation (1, 2, 3)
            kept_variations = set()
            kept_entries = []
            
            # Sort by prompt_variation to prioritize keeping different variations
            entries.sort(key=lambda x: x.get('prompt_variation', 1))
            
            for entry in entries:
                variation = entry.get('prompt_variation', 1)
                
                # Keep if we haven't seen this variation yet, or if we have less than 3 total
                if variation not in kept_variations or len(kept_entries) < 3:
                    kept_entries.append(entry)
                    kept_variations.add(variation)
                    
                    # Stop if we have 3 descriptions
                    if len(kept_entries) >= 3:
                        break
            
            cleaned_data.extend(kept_entries)
            removed_count = original_count - len(kept_entries)
            total_removed += removed_count
            
            if removed_count > 0:
                print(f"   📝 {image_filename}: kept {len(kept_entries)}/{original_count} descriptions")
    
    print(f"✅ Smart cleaning completed:")
    print(f"   Original entries: {len(data)}")
    print(f"   Cleaned entries: {len(cleaned_data)}")
    print(f"   Removed entries: {total_removed}")
    print(f"   Images with exact duplicates: {exact_duplicates_found}")
    
    return cleaned_data

def clean_batch_file(input_file_path, create_backup=True):
    """Clean a batch file and create a cleaned copy."""
    input_path = Path(input_file_path)
    
    if not input_path.exists():
        print(f"❌ File not found: {input_path}")
        return None
    
    print(f"🚀 Cleaning batch file: {input_path.name}")
    print("=" * 60)
    
    # Create backup if requested
    if create_backup:
        backup_path = input_path.parent / f"{input_path.stem}_original_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        shutil.copy2(input_path, backup_path)
        print(f"💾 Created backup: {backup_path.name}")
    
    # Load the data
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Loaded {len(data)} entries")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return None
    
    # Analyze duplicates
    images_with_excess, count_distribution = analyze_duplicates(data)
    
    if not images_with_excess:
        print("✅ No duplicates found! File is already clean.")
        return input_path
    
    # Clean the data
    cleaned_data = clean_duplicates_smart(data)
    
    # Create cleaned file
    cleaned_path = input_path.parent / f"{input_path.stem}_cleaned.json"
    
    try:
        with open(cleaned_path, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, indent=2, ensure_ascii=False)
        print(f"✅ Cleaned file saved: {cleaned_path.name}")
        
        # Verify the cleaned file
        print(f"\n🔍 Verification:")
        with open(cleaned_path, 'r', encoding='utf-8') as f:
            verification_data = json.load(f)
        
        verify_images_with_excess, verify_distribution = analyze_duplicates(verification_data)
        
        if not verify_images_with_excess:
            print("✅ Verification passed: No excess descriptions in cleaned file")
        else:
            print(f"⚠️  Verification warning: Still {len(verify_images_with_excess)} images with excess descriptions")
        
        return cleaned_path
        
    except Exception as e:
        print(f"❌ Error saving cleaned file: {e}")
        return None

def main():
    """Main execution."""
    # Target the problematic tench batch file
    batch_file = "raw_results/batch_0048_tench_results.json"
    
    print("🧹 Batch File Duplicate Cleaner")
    print("=" * 40)
    
    cleaned_file = clean_batch_file(batch_file, create_backup=True)
    
    if cleaned_file:
        print(f"\n🎉 SUCCESS!")
        print(f"📁 Original file: {batch_file}")
        print(f"📁 Cleaned file: {cleaned_file.name}")
        print(f"📁 Backup created with timestamp")
        print(f"\n💡 The original file is preserved. You can use the cleaned version for processing.")
    else:
        print(f"\n❌ FAILED to clean the batch file")

if __name__ == "__main__":
    main()
