#!/usr/bin/env python3
"""
Reliability Test for CLIP Optimizer

This script tests the reliability and performance of the CLIP optimizer
before running it on the full dataset.
"""

import time
import json
from pathlib import Path
from clip_optimizer import CLIPDescriptionOptimizer

def run_reliability_test(project_id: str, num_tests: int = 20):
    """Run comprehensive reliability tests."""
    print(f"🔬 Running Reliability Test")
    print(f"=" * 40)
    print(f"📊 Testing {num_tests} optimization requests...")
    
    # Initialize optimizer
    optimizer = CLIPDescriptionOptimizer(project_id)
    
    # Test descriptions of varying lengths
    test_descriptions = [
        # Short description (should pass through)
        "A red car on the street.",
        
        # Medium description (needs optimization)
        "This image features a white municipal garbage truck with a large cab and a substantial waste container body, indicative of heavy-duty commercial proportions and large waste capacity.",
        
        # Long description (needs aggressive optimization)
        "This image features a white, heavy-duty garbage truck with a large cab and a substantial waste container body. It displays municipal markings, including \"KFW\" and a phone number, indicating its commercial service role. The truck is equipped with a rear loader mechanism and hydraulic arms, designed for efficient waste collection in an urban operational setting. Its robust chassis and large capacity highlight its utility as a municipal vehicle.",
        
        # Very long description (maximum challenge)
        "**Vehicle Comparison:** Distinctive for its large, enclosed hopper and hydraulic lifting arm, differentiating it from standard trucks. Its robust build and specialized collection equipment are key identifiers. Municipal service markers, like company logos (\"KFW\"), are prominent. **Service Context:** This vehicle operates in urban/suburban environments, interacting with residential waste bins. Its composition includes a heavy-duty chassis and a large-capacity compaction body, crucial for efficient waste collection. **Municipal Function:** Designed to municipal waste collection standards, its utility lies in operational efficiency and service delivery. The overall harmony of its specialized components clearly represents its municipal service role.",
        
        # Edge case: Already optimized
        "White garbage truck with hydraulic arms, municipal markings, urban setting, waste collection vehicle."
    ]
    
    # Test statistics
    results = {
        'total_tests': 0,
        'successful_optimizations': 0,
        'failed_optimizations': 0,
        'token_limit_violations': 0,
        'api_errors': 0,
        'response_times': [],
        'token_reductions': [],
        'success_by_length': {'short': 0, 'medium': 0, 'long': 0, 'very_long': 0, 'optimized': 0},
        'total_by_length': {'short': 0, 'medium': 0, 'long': 0, 'very_long': 0, 'optimized': 0}
    }
    
    # Run tests
    for test_round in range(num_tests):
        print(f"\n🧪 Test Round {test_round + 1}/{num_tests}")
        
        for i, description in enumerate(test_descriptions):
            original_tokens = optimizer.count_tokens(description)
            
            # Categorize by length
            if original_tokens <= 20:
                length_category = 'short'
            elif original_tokens <= 50:
                length_category = 'medium'
            elif original_tokens <= 100:
                length_category = 'long'
            elif original_tokens <= 200:
                length_category = 'very_long'
            else:
                length_category = 'optimized'
            
            results['total_by_length'][length_category] += 1
            
            print(f"   📝 Test {i+1}: {original_tokens} tokens → ", end="", flush=True)
            
            # Time the optimization
            start_time = time.time()
            
            try:
                optimized, success, final_tokens = optimizer.optimize_description(description)
                response_time = time.time() - start_time
                
                results['total_tests'] += 1
                results['response_times'].append(response_time)
                
                if success:
                    results['successful_optimizations'] += 1
                    results['success_by_length'][length_category] += 1
                    
                    if final_tokens <= optimizer.max_tokens:
                        print(f"{final_tokens} tokens ✅ ({response_time:.1f}s)")
                        if original_tokens > final_tokens:
                            results['token_reductions'].append(original_tokens - final_tokens)
                    else:
                        print(f"{final_tokens} tokens ⚠️ EXCEEDS LIMIT ({response_time:.1f}s)")
                        results['token_limit_violations'] += 1
                else:
                    print(f"FAILED ❌ ({response_time:.1f}s)")
                    results['failed_optimizations'] += 1
                    
            except Exception as e:
                response_time = time.time() - start_time
                print(f"ERROR ❌ ({response_time:.1f}s): {str(e)[:50]}...")
                results['api_errors'] += 1
                results['failed_optimizations'] += 1
                results['total_tests'] += 1
        
        # Progress update
        if (test_round + 1) % 5 == 0:
            current_success_rate = (results['successful_optimizations'] / results['total_tests'] * 100)
            print(f"\n   📊 Progress: {current_success_rate:.1f}% success rate so far")
    
    return results

def analyze_results(results):
    """Analyze and display test results."""
    print(f"\n📊 RELIABILITY TEST RESULTS")
    print(f"=" * 50)
    
    # Overall statistics
    total_tests = results['total_tests']
    success_rate = (results['successful_optimizations'] / total_tests * 100) if total_tests > 0 else 0
    
    print(f"📈 Overall Performance:")
    print(f"   Total tests: {total_tests}")
    print(f"   Successful optimizations: {results['successful_optimizations']}")
    print(f"   Failed optimizations: {results['failed_optimizations']}")
    print(f"   Success rate: {success_rate:.1f}%")
    print(f"   Token limit violations: {results['token_limit_violations']}")
    print(f"   API errors: {results['api_errors']}")
    
    # Response time analysis
    if results['response_times']:
        avg_response_time = sum(results['response_times']) / len(results['response_times'])
        max_response_time = max(results['response_times'])
        min_response_time = min(results['response_times'])
        
        print(f"\n⏱️  Response Time Analysis:")
        print(f"   Average: {avg_response_time:.1f}s")
        print(f"   Min: {min_response_time:.1f}s")
        print(f"   Max: {max_response_time:.1f}s")
    
    # Token reduction analysis
    if results['token_reductions']:
        avg_reduction = sum(results['token_reductions']) / len(results['token_reductions'])
        total_tokens_saved = sum(results['token_reductions'])
        
        print(f"\n💰 Token Efficiency:")
        print(f"   Average tokens saved per optimization: {avg_reduction:.1f}")
        print(f"   Total tokens saved: {total_tokens_saved}")
    
    # Success rate by description length
    print(f"\n📏 Success Rate by Description Length:")
    for length_cat in ['short', 'medium', 'long', 'very_long', 'optimized']:
        if results['total_by_length'][length_cat] > 0:
            cat_success_rate = (results['success_by_length'][length_cat] / results['total_by_length'][length_cat] * 100)
            print(f"   {length_cat.capitalize()}: {cat_success_rate:.1f}% ({results['success_by_length'][length_cat]}/{results['total_by_length'][length_cat]})")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    
    if success_rate >= 95:
        print(f"   ✅ Excellent reliability! Ready for full dataset processing.")
    elif success_rate >= 85:
        print(f"   ✅ Good reliability. Consider monitoring during full processing.")
    elif success_rate >= 70:
        print(f"   ⚠️  Moderate reliability. Consider increasing delays or retries.")
    else:
        print(f"   ❌ Poor reliability. Investigation needed before full processing.")
    
    if results['token_limit_violations'] > 0:
        print(f"   ⚠️  {results['token_limit_violations']} token limit violations detected.")
        print(f"      Consider more aggressive optimization prompts.")
    
    if results['api_errors'] > total_tests * 0.1:  # More than 10% API errors
        print(f"   ⚠️  High API error rate ({results['api_errors']}/{total_tests}).")
        print(f"      Check network connectivity and API quotas.")
    
    if results['response_times'] and max(results['response_times']) > 30:
        print(f"   ⚠️  Some requests took longer than 30 seconds.")
        print(f"      Consider implementing request timeouts.")
    
    return success_rate >= 85  # Return True if reliability is acceptable

def estimate_full_processing(results, total_descriptions=28000):
    """Estimate time and cost for full dataset processing."""
    if not results['response_times']:
        return
    
    print(f"\n🔮 Full Dataset Processing Estimates:")
    print(f"   Target descriptions: {total_descriptions:,}")
    
    # Time estimation
    avg_time_per_desc = sum(results['response_times']) / len(results['response_times'])
    estimated_total_time = avg_time_per_desc * total_descriptions
    estimated_hours = estimated_total_time / 3600
    
    print(f"   Estimated processing time: {estimated_hours:.1f} hours")
    
    # Failure estimation
    success_rate = (results['successful_optimizations'] / results['total_tests'])
    estimated_failures = total_descriptions * (1 - success_rate)
    
    print(f"   Estimated failures: {estimated_failures:.0f} descriptions")
    
    # Cost estimation (rough)
    # Assuming average 100 tokens input, 50 tokens output per request
    estimated_input_tokens = total_descriptions * 100
    estimated_output_tokens = total_descriptions * 50
    
    # Gemini 2.5 Flash Lite pricing (approximate)
    input_cost = estimated_input_tokens / 1_000_000 * 0.075  # $0.075 per 1M input tokens
    output_cost = estimated_output_tokens / 1_000_000 * 0.30  # $0.30 per 1M output tokens
    total_cost = input_cost + output_cost
    
    print(f"   Estimated cost: ${total_cost:.2f} USD")

def main():
    """Main test execution."""
    project_id = "brilliant-brand-466317-j2"  # Your confirmed working project ID
    
    print(f"🔬 CLIP Optimizer Reliability Test")
    print(f"📍 Project: {project_id}")
    print(f"🎯 Testing optimization reliability before full dataset processing")
    
    try:
        # Run reliability test
        results = run_reliability_test(project_id, num_tests=10)  # 10 rounds = 50 total tests
        
        # Analyze results
        is_reliable = analyze_results(results)
        
        # Estimate full processing
        estimate_full_processing(results)
        
        # Final recommendation
        print(f"\n🎯 FINAL RECOMMENDATION:")
        if is_reliable:
            print(f"✅ System is reliable enough for full dataset processing!")
            print(f"💡 You can proceed with: python clip_optimizer.py")
        else:
            print(f"⚠️  System needs improvement before full dataset processing.")
            print(f"💡 Consider adjusting retry settings or investigating API issues.")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        print(f"💡 Check your Google Cloud setup and project configuration.")

if __name__ == "__main__":
    main()
