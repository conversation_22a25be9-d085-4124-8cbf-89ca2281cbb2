#!/usr/bin/env python3
"""
ImageNette CCSO Dataset Extraction Script
Extracts descriptions from batch files and creates CCSO-ready dataset
"""

import json
import os
from pathlib import Path
import re
from collections import defaultdict
from datetime import datetime

class ImageNetteCCSOExtractor:
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.raw_results_dir = self.base_path / "raw_results"
        self.output_dir = self.base_path / "extracted_ccso_dataset"
        
        # ImageNette classes
        self.imagenette_classes = [
            'tench', 'English springer', 'cassette player', 'chain saw', 
            'church', 'French horn', 'garbage truck', 'gas pump', 
            'golf ball', 'parachute'
        ]
        
        # Create output directory
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"🔍 Initialized extractor for: {self.base_path}")
        print(f"📁 Raw results directory: {self.raw_results_dir}")
        print(f"📁 Output directory: {self.output_dir}")

    def find_batch_files(self):
        """Find all batch files and organize by class."""
        print("\n🔍 Scanning batch files...")
        
        batch_files = list(self.raw_results_dir.glob("batch_*.json"))
        print(f"Found {len(batch_files)} batch files")
        
        # Group by class
        class_batches = defaultdict(list)
        
        for batch_file in batch_files:
            # Extract class name from filename: batch_XXXX_CLASSNAME_results.json
            match = re.search(r'batch_(\d+)_(.+)_results\.json', batch_file.name)
            if match:
                batch_num = int(match.group(1))
                class_name = match.group(2)
                class_batches[class_name].append((batch_num, batch_file))
        
        # Sort by batch number and get the last one for each class
        last_batches = {}
        for class_name, batches in class_batches.items():
            batches.sort(key=lambda x: x[0])  # Sort by batch number
            last_batch_num, last_batch_file = batches[-1]
            last_batches[class_name] = {
                'file': last_batch_file,
                'batch_num': last_batch_num,
                'total_batches': len(batches)
            }
        
        print(f"\n📊 Found classes and their last batches:")
        for class_name, info in last_batches.items():
            print(f"   {class_name}: batch_{info['batch_num']:04d} ({info['total_batches']} total batches)")
        
        return last_batches

    def extract_descriptions_from_batch(self, batch_file):
        """Extract all descriptions from a batch file."""
        print(f"📖 Reading {batch_file.name}...")
        
        try:
            with open(batch_file, 'r', encoding='utf-8') as f:
                batch_data = json.load(f)
        except Exception as e:
            print(f"❌ Error reading {batch_file}: {e}")
            return []
        
        descriptions = []
        successful_count = 0
        failed_count = 0
        
        for entry in batch_data:
            if entry.get('success', False):
                descriptions.append({
                    'description': entry['description'],
                    'prompt_variation': entry.get('prompt_variation', 1),
                    'image_filename': entry.get('image_filename', ''),
                    'tokens': entry.get('output_tokens', 0),
                    'cost': entry.get('cost', 0.0)
                })
                successful_count += 1
            else:
                failed_count += 1
        
        print(f"   ✅ Extracted {successful_count} descriptions ({failed_count} failed)")
        return descriptions

    def organize_by_class(self, last_batches):
        """Extract descriptions for each class and organize them."""
        print(f"\n🗂️  Extracting descriptions by class...")
        
        class_descriptions = {}
        total_descriptions = 0
        processing_stats = {
            'classes_processed': 0,
            'total_descriptions': 0,
            'total_images': 0,
            'total_cost': 0.0,
            'prompt_variations': set(),
            'extraction_timestamp': datetime.now().isoformat()
        }
        
        for class_name, batch_info in last_batches.items():
            print(f"\n🎯 Processing {class_name}...")
            
            descriptions = self.extract_descriptions_from_batch(batch_info['file'])
            
            if descriptions:
                # Group by image to count unique images
                images_set = set()
                prompt_variations_used = set()
                class_cost = 0.0
                
                # Extract just the descriptions for CCSO
                description_texts = []
                for desc in descriptions:
                    description_texts.append(desc['description'])
                    images_set.add(desc['image_filename'])
                    prompt_variations_used.add(desc['prompt_variation'])
                    class_cost += desc['cost']
                
                class_descriptions[class_name] = {
                    'descriptions': description_texts,
                    'metadata': {
                        'total_descriptions': len(description_texts),
                        'unique_images': len(images_set),
                        'descriptions_per_image': len(description_texts) / len(images_set) if images_set else 0,
                        'prompt_variations_used': sorted(list(prompt_variations_used)),
                        'total_cost': class_cost,
                        'avg_tokens': sum(d['tokens'] for d in descriptions) / len(descriptions) if descriptions else 0
                    }
                }
                
                # Update overall stats
                processing_stats['total_descriptions'] += len(description_texts)
                processing_stats['total_images'] += len(images_set)
                processing_stats['total_cost'] += class_cost
                processing_stats['prompt_variations'].update(prompt_variations_used)
                processing_stats['classes_processed'] += 1
                
                print(f"   📝 {len(description_texts)} descriptions from {len(images_set)} images")
                print(f"   💰 Class cost: ${class_cost:.4f}")
            else:
                print(f"   ❌ No descriptions found for {class_name}")
        
        # Convert set to list for JSON serialization
        processing_stats['prompt_variations'] = sorted(list(processing_stats['prompt_variations']))
        
        return class_descriptions, processing_stats

    def create_ccso_datasets(self, class_descriptions, processing_stats):
        """Create CCSO-ready datasets in multiple formats."""
        print(f"\n📦 Creating CCSO datasets...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. Simple CCSO format (class -> descriptions list)
        ccso_simple = {}
        for class_name, data in class_descriptions.items():
            ccso_simple[class_name] = data['descriptions']
        
        simple_file = self.output_dir / f"imagenette_ccso_simple_{timestamp}.json"
        with open(simple_file, 'w', encoding='utf-8') as f:
            json.dump(ccso_simple, f, indent=2, ensure_ascii=False)
        print(f"✅ Simple CCSO format: {simple_file}")
        
        # 2. Enhanced CCSO format (with metadata)
        enhanced_file = self.output_dir / f"imagenette_ccso_enhanced_{timestamp}.json"
        with open(enhanced_file, 'w', encoding='utf-8') as f:
            json.dump(class_descriptions, f, indent=2, ensure_ascii=False)
        print(f"✅ Enhanced CCSO format: {enhanced_file}")
        
        # 3. Flat descriptions list (for some algorithms)
        flat_descriptions = []
        flat_labels = []
        for class_name, data in class_descriptions.items():
            for description in data['descriptions']:
                flat_descriptions.append(description)
                flat_labels.append(class_name)
        
        flat_format = {
            'descriptions': flat_descriptions,
            'labels': flat_labels,
            'metadata': {
                'total_samples': len(flat_descriptions),
                'classes': list(class_descriptions.keys()),
                'num_classes': len(class_descriptions)
            }
        }
        
        flat_file = self.output_dir / f"imagenette_ccso_flat_{timestamp}.json"
        with open(flat_file, 'w', encoding='utf-8') as f:
            json.dump(flat_format, f, indent=2, ensure_ascii=False)
        print(f"✅ Flat CCSO format: {flat_file}")
        
        # 4. Processing statistics
        stats_file = self.output_dir / f"imagenette_extraction_stats_{timestamp}.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(processing_stats, f, indent=2, ensure_ascii=False)
        print(f"✅ Extraction statistics: {stats_file}")
        
        # 5. README file
        readme_content = f"""# ImageNette CCSO Dataset
Extracted on: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## Dataset Statistics:
- Classes: {processing_stats['classes_processed']}
- Total descriptions: {processing_stats['total_descriptions']}
- Total images: {processing_stats['total_images']}
- Descriptions per image: {processing_stats['total_descriptions'] / processing_stats['total_images']:.1f}
- Total cost: ${processing_stats['total_cost']:.4f}
- Prompt variations: {processing_stats['prompt_variations']}

## Files:
1. `imagenette_ccso_simple_*.json` - Simple format: {{class: [descriptions]}}
2. `imagenette_ccso_enhanced_*.json` - Enhanced format with metadata
3. `imagenette_ccso_flat_*.json` - Flat format: {{descriptions: [...], labels: [...]}}
4. `imagenette_extraction_stats_*.json` - Processing statistics

## Classes:
{chr(10).join(f"- {name}: {data['metadata']['total_descriptions']} descriptions" for name, data in class_descriptions.items())}

## Usage:
- For CCSO clustering: Use simple or enhanced format
- For other ML algorithms: Use flat format
- All formats contain the same description data in different organizations
"""
        
        readme_file = self.output_dir / f"README_{timestamp}.md"
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✅ Documentation: {readme_file}")
        
        return {
            'simple': simple_file,
            'enhanced': enhanced_file,
            'flat': flat_file,
            'stats': stats_file,
            'readme': readme_file
        }

    def print_summary(self, class_descriptions, processing_stats, output_files):
        """Print extraction summary."""
        print(f"\n🎉 EXTRACTION COMPLETED!")
        print(f"=" * 50)
        print(f"📊 Summary Statistics:")
        print(f"   Classes processed: {processing_stats['classes_processed']}")
        print(f"   Total descriptions: {processing_stats['total_descriptions']}")
        print(f"   Total unique images: {processing_stats['total_images']}")
        print(f"   Avg descriptions per image: {processing_stats['total_descriptions'] / processing_stats['total_images']:.1f}")
        print(f"   Total processing cost: ${processing_stats['total_cost']:.4f}")
        print(f"   Prompt variations used: {processing_stats['prompt_variations']}")
        
        print(f"\n📋 Per-Class Breakdown:")
        for class_name, data in class_descriptions.items():
            meta = data['metadata']
            print(f"   {class_name}:")
            print(f"     Descriptions: {meta['total_descriptions']}")
            print(f"     Images: {meta['unique_images']}")
            print(f"     Avg per image: {meta['descriptions_per_image']:.1f}")
            print(f"     Cost: ${meta['total_cost']:.4f}")
        
        print(f"\n📁 Output Files Created:")
        for file_type, file_path in output_files.items():
            print(f"   {file_type.title()}: {file_path.name}")
        
        print(f"\n🎯 Ready for CCSO Clustering!")
        print(f"   Primary file: {output_files['simple'].name}")
        print(f"   Use enhanced file for detailed analysis")
        print(f"   Use flat file for algorithms requiring array format")

    def extract_complete_dataset(self):
        """Complete extraction pipeline."""
        print("🚀 Starting ImageNette CCSO Dataset Extraction")
        print("=" * 50)
        
        # Step 1: Find batch files
        last_batches = self.find_batch_files()
        
        if not last_batches:
            print("❌ No batch files found!")
            return None
        
        # Step 2: Extract descriptions by class
        class_descriptions, processing_stats = self.organize_by_class(last_batches)
        
        if not class_descriptions:
            print("❌ No descriptions extracted!")
            return None
        
        # Step 3: Create CCSO datasets
        output_files = self.create_ccso_datasets(class_descriptions, processing_stats)
        
        # Step 4: Print summary
        self.print_summary(class_descriptions, processing_stats, output_files)
        
        return output_files

def main():
    """Main execution function."""
    # Configuration - Update this path to your ImageNette dataset location
    BASE_PATH = "/content/drive/MyDrive/ImageNette_CCSO_Dataset"
    
    # For laptop execution, update to your local path:
    # BASE_PATH = "./ImageNette_CCSO_Dataset"  # Uncomment for laptop
    
    print("🎯 ImageNette CCSO Dataset Extractor")
    print("=====================================")
    
    # Initialize extractor
    extractor = ImageNetteCCSOExtractor(BASE_PATH)
    
    # Extract complete dataset
    output_files = extractor.extract_complete_dataset()
    
    if output_files:
        print(f"\n✅ SUCCESS! Dataset extracted and ready for use.")
        print(f"📁 All files saved in: {extractor.output_dir}")
    else:
        print(f"\n❌ FAILED! Could not extract dataset.")

if __name__ == "__main__":
    main()