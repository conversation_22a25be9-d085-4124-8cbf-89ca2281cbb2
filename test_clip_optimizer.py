#!/usr/bin/env python3
"""
Test script for CLIP Description Optimizer

This script tests the optimizer with a small sample to verify setup and functionality.
"""

import json
import os
from pathlib import Path
from clip_optimizer import CLIPDescriptionOptimizer

def create_test_data():
    """Create a small test dataset."""
    test_data = {
        "classes": {
            "test_class": {
                "descriptions": [
                    "This is a very long description that definitely exceeds the 77 token limit for CLIP models and needs to be optimized to work properly with the contrastive language-image pre-training architecture that has this specific limitation built into its design.",
                    "Short description.",
                    "Another moderately long description that might be close to the token limit but should be optimized to ensure it works well with CLIP models and provides good semantic understanding for image-text matching tasks."
                ],
                "metadata": {
                    "total_descriptions": 3,
                    "test_data": True
                }
            }
        },
        "processing_info": {
            "test_dataset": True,
            "created_for": "testing_clip_optimizer"
        }
    }
    
    test_file = "test_input.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, indent=2)
    
    return test_file

def test_token_counting():
    """Test the token counting functionality."""
    print("🧪 Testing token counting...")
    
    try:
        import tiktoken
        tokenizer = tiktoken.get_encoding("cl100k_base")
        
        test_texts = [
            "Short text",
            "This is a longer text that should have more tokens",
            "A very long description with many words that will definitely exceed the typical token limits"
        ]
        
        for text in test_texts:
            tokens = len(tokenizer.encode(text))
            print(f"   '{text[:30]}...' → {tokens} tokens")
        
        print("   ✅ Token counting works correctly")
        return True
        
    except Exception as e:
        print(f"   ❌ Token counting failed: {e}")
        return False

def test_optimization(project_id: str):
    """Test the optimization process with sample data."""
    print("\n🧪 Testing CLIP optimization...")
    
    try:
        # Create test data
        test_file = create_test_data()
        print(f"   📝 Created test file: {test_file}")
        
        # Initialize optimizer
        optimizer = CLIPDescriptionOptimizer(project_id)
        
        # Test single description optimization
        test_desc = "This is a very long description that definitely exceeds the 77 token limit for CLIP models and needs to be optimized."
        original_tokens = optimizer.count_tokens(test_desc)
        print(f"   📊 Original description: {original_tokens} tokens")
        
        optimized, success, final_tokens = optimizer.optimize_description(test_desc)
        
        if success:
            print(f"   ✅ Optimization successful: {final_tokens} tokens")
            print(f"   📝 Optimized: '{optimized}'")
        else:
            print(f"   ❌ Optimization failed")
        
        # Clean up
        if Path(test_file).exists():
            Path(test_file).unlink()
        
        return success
        
    except Exception as e:
        print(f"   ❌ Optimization test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🔬 CLIP Optimizer Test Suite")
    print("=" * 40)
    
    # Test 1: Token counting
    token_test_passed = test_token_counting()
    
    # Test 2: Check dependencies
    print("\n🧪 Testing dependencies...")
    try:
        import tiktoken
        import vertexai
        from google.cloud import aiplatform
        print("   ✅ All dependencies installed")
        deps_ok = True
    except ImportError as e:
        print(f"   ❌ Missing dependency: {e}")
        deps_ok = False
    
    # Test 3: Check authentication (optional)
    print("\n🧪 Testing Google Cloud authentication...")
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT')
    if not project_id:
        project_id = input("   Enter your Google Cloud Project ID (or press Enter to skip): ").strip()
    
    if project_id:
        try:
            # Test basic Vertex AI initialization
            import vertexai
            vertexai.init(project=project_id, location="us-central1")
            print("   ✅ Google Cloud authentication works")
            auth_ok = True
            
            # Test optimization if everything else works
            if token_test_passed and deps_ok:
                opt_test_passed = test_optimization(project_id)
            else:
                opt_test_passed = False
                
        except Exception as e:
            print(f"   ⚠️  Authentication issue: {e}")
            print("   💡 Make sure you've set up Google Cloud credentials")
            auth_ok = False
            opt_test_passed = False
    else:
        print("   ⏭️  Skipping authentication test")
        auth_ok = None
        opt_test_passed = False
    
    # Summary
    print(f"\n📋 Test Results Summary:")
    print(f"   Token counting: {'✅' if token_test_passed else '❌'}")
    print(f"   Dependencies: {'✅' if deps_ok else '❌'}")
    print(f"   Authentication: {'✅' if auth_ok else '⚠️' if auth_ok is False else '⏭️'}")
    print(f"   Optimization: {'✅' if opt_test_passed else '❌' if not opt_test_passed and auth_ok else '⏭️'}")
    
    if token_test_passed and deps_ok and auth_ok:
        print(f"\n🎉 All tests passed! The optimizer is ready to use.")
        print(f"💡 Run 'python clip_optimizer.py' to optimize your dataset.")
    else:
        print(f"\n⚠️  Some tests failed. Please check the setup:")
        if not deps_ok:
            print(f"   📦 Install dependencies: pip install -r requirements_clip_optimizer.txt")
        if not auth_ok and auth_ok is not None:
            print(f"   🔐 Set up Google Cloud authentication")
        if not token_test_passed:
            print(f"   🔧 Check tiktoken installation")

if __name__ == "__main__":
    main()
