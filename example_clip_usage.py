#!/usr/bin/env python3
"""
Example: Using CLIP-Optimized Descriptions

This script demonstrates how to use the optimized descriptions with CLIP models
for image-text similarity tasks.
"""

import json
import torch
import clip
from PIL import Image
import numpy as np
from pathlib import Path

def load_optimized_descriptions(file_path: str):
    """Load the CLIP-optimized descriptions."""
    print(f"📂 Loading optimized descriptions from: {file_path}")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Extract all descriptions with their class labels
    descriptions = []
    labels = []
    
    for class_name, class_data in data['classes'].items():
        for desc in class_data.get('descriptions', []):
            descriptions.append(desc)
            labels.append(class_name)
    
    print(f"   ✅ Loaded {len(descriptions)} descriptions from {len(data['classes'])} classes")
    return descriptions, labels, data

def setup_clip_model(device=None):
    """Initialize CLIP model."""
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
    
    print(f"🤖 Loading CLIP model on {device}...")
    
    # Load CLIP model (you can change the model variant)
    model, preprocess = clip.load("ViT-B/32", device=device)
    
    print(f"   ✅ CLIP model loaded successfully")
    return model, preprocess, device

def encode_descriptions(model, descriptions, device, batch_size=32):
    """Encode descriptions using CLIP text encoder."""
    print(f"📝 Encoding {len(descriptions)} descriptions...")
    
    all_text_features = []
    
    # Process in batches to avoid memory issues
    for i in range(0, len(descriptions), batch_size):
        batch_descriptions = descriptions[i:i + batch_size]
        
        # Tokenize descriptions
        text_tokens = clip.tokenize(batch_descriptions).to(device)
        
        # Encode with CLIP
        with torch.no_grad():
            text_features = model.encode_text(text_tokens)
            text_features = text_features / text_features.norm(dim=-1, keepdim=True)  # Normalize
        
        all_text_features.append(text_features.cpu())
        
        if i % (batch_size * 10) == 0:
            print(f"   📊 Processed {min(i + batch_size, len(descriptions))}/{len(descriptions)} descriptions")
    
    # Concatenate all features
    text_features = torch.cat(all_text_features, dim=0)
    print(f"   ✅ Text encoding complete: {text_features.shape}")
    
    return text_features

def find_best_descriptions(image_path: str, model, preprocess, text_features, descriptions, labels, device, top_k=5):
    """Find the best matching descriptions for an image."""
    print(f"🖼️  Processing image: {image_path}")
    
    # Load and preprocess image
    image = Image.open(image_path)
    image_input = preprocess(image).unsqueeze(0).to(device)
    
    # Encode image
    with torch.no_grad():
        image_features = model.encode_image(image_input)
        image_features = image_features / image_features.norm(dim=-1, keepdim=True)
    
    # Calculate similarities
    similarities = (image_features.cpu() @ text_features.T).squeeze(0)
    
    # Get top-k matches
    top_indices = similarities.argsort(descending=True)[:top_k]
    
    print(f"   🎯 Top {top_k} matching descriptions:")
    results = []
    for i, idx in enumerate(top_indices):
        similarity = similarities[idx].item()
        description = descriptions[idx]
        label = labels[idx]
        
        print(f"      {i+1}. [{label}] (similarity: {similarity:.3f})")
        print(f"         \"{description}\"")
        
        results.append({
            'rank': i + 1,
            'similarity': similarity,
            'description': description,
            'class': label
        })
    
    return results

def analyze_optimization_quality(data):
    """Analyze the quality of the optimization."""
    print(f"\n📊 Analyzing optimization quality...")
    
    optimization_info = data.get('processing_info', {}).get('clip_optimization', {})
    
    if optimization_info:
        stats = optimization_info.get('statistics', {})
        print(f"   📈 Optimization Statistics:")
        print(f"      Total descriptions: {stats.get('total_descriptions', 'N/A')}")
        print(f"      Successfully optimized: {stats.get('optimized_descriptions', 'N/A')}")
        print(f"      Success rate: {stats.get('optimized_descriptions', 0) / max(stats.get('total_descriptions', 1), 1) * 100:.1f}%")
        print(f"      Tokens saved: {stats.get('token_savings', 'N/A')}")
        print(f"      Processing time: {stats.get('processing_time', 'N/A'):.1f} seconds")
    else:
        print(f"   ⚠️  No optimization metadata found")

def main():
    """Main demonstration function."""
    print("🎯 CLIP-Optimized Descriptions Demo")
    print("=" * 40)
    
    # Configuration
    OPTIMIZED_FILE = "extracted_ccso_dataset/imagenette_ccso_enhanced_robust_20250821_135956_clip_optimized.json"
    SAMPLE_IMAGE = "sample_image.jpg"  # Replace with path to your test image
    
    try:
        # Check if optimized file exists
        if not Path(OPTIMIZED_FILE).exists():
            print(f"❌ Optimized file not found: {OPTIMIZED_FILE}")
            print(f"💡 Run 'python clip_optimizer.py' first to create the optimized descriptions")
            return
        
        # Load optimized descriptions
        descriptions, labels, data = load_optimized_descriptions(OPTIMIZED_FILE)
        
        # Analyze optimization quality
        analyze_optimization_quality(data)
        
        # Setup CLIP model
        model, preprocess, device = setup_clip_model()
        
        # Encode all descriptions
        text_features = encode_descriptions(model, descriptions, device)
        
        # Example: Find best descriptions for an image
        if Path(SAMPLE_IMAGE).exists():
            results = find_best_descriptions(
                SAMPLE_IMAGE, model, preprocess, text_features, 
                descriptions, labels, device
            )
        else:
            print(f"\n💡 To test image matching, place a test image at: {SAMPLE_IMAGE}")
        
        # Show some example optimized descriptions
        print(f"\n📝 Sample optimized descriptions:")
        for class_name in list(data['classes'].keys())[:3]:  # Show first 3 classes
            class_descriptions = data['classes'][class_name]['descriptions']
            print(f"\n   🏷️  {class_name}:")
            for i, desc in enumerate(class_descriptions[:2]):  # Show first 2 descriptions
                tokens = len(clip.tokenize([desc])[0])
                print(f"      {i+1}. ({tokens} tokens) \"{desc}\"")
        
        print(f"\n✅ Demo completed successfully!")
        print(f"💡 The optimized descriptions are ready for CLIP-based applications!")
        
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print(f"💡 Install CLIP: pip install git+https://github.com/openai/CLIP.git")
        print(f"💡 Install PyTorch: pip install torch torchvision")
        print(f"💡 Install Pillow: pip install Pillow")
    except Exception as e:
        print(f"❌ Error during demo: {e}")

if __name__ == "__main__":
    main()
