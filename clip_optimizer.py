#!/usr/bin/env python3
"""
CLIP Description Optimizer using Google Vertex AI Gemini 2.5 Flash Lite

This script optimizes image descriptions for CLIP compatibility by:
1. Loading descriptions from a JSON file
2. Using Vertex AI Gemini to rewrite descriptions to be concise and object-focused
3. Ensuring all descriptions are ≤ 77 tokens (CLIP's hard limit)
4. Preserving original JSON structure and metadata
5. Saving optimized descriptions to a new file
"""

import json
import os
import time
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import tiktoken
import vertexai
from vertexai.generative_models import GenerativeModel, Part
from google.cloud import aiplatform

class CLIPDescriptionOptimizer:
    def __init__(self, project_id: str, location: str = "us-central1"):
        """Initialize the CLIP optimizer with Vertex AI credentials."""
        self.project_id = project_id
        self.location = location
        self.max_tokens = 77  # CLIP's hard architectural limit
        self.max_retries = 3
        self.retry_delay = 2  # seconds
        
        # Initialize Vertex AI
        vertexai.init(project=project_id, location=location)
        self.model = GenerativeModel("gemini-2.5-flash-lite")
        
        # Initialize tokenizer for accurate token counting
        # Using cl100k_base encoding which is similar to CLIP's tokenizer
        self.tokenizer = tiktoken.get_encoding("cl100k_base")
        
        # Statistics tracking
        self.stats = {
            'total_descriptions': 0,
            'optimized_descriptions': 0,
            'failed_descriptions': 0,
            'token_savings': 0,
            'processing_time': 0,
            'api_calls': 0,
            'retries': 0
        }
        
        print(f"🚀 Initialized CLIP Description Optimizer")
        print(f"📍 Project: {project_id}, Location: {location}")
        print(f"🎯 Target: ≤ {self.max_tokens} tokens per description")

    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken."""
        return len(self.tokenizer.encode(text))

    def create_optimization_prompt(self, description: str, current_tokens: int, attempt: int = 1) -> str:
        """Create a prompt for optimizing the description."""
        if attempt == 1:
            # First attempt: comprehensive optimization
            return f"""You are an expert at creating concise, object-focused descriptions for CLIP (Contrastive Language-Image Pre-training) models.

CRITICAL REQUIREMENTS:
- Output must be ≤ 77 tokens (this is a hard architectural limit)
- Focus on visual objects, colors, shapes, and spatial relationships
- Use simple, clear language that CLIP understands well
- Avoid complex sentences, abstract concepts, or lengthy explanations
- Prioritize nouns, adjectives, and spatial descriptors
- Remove unnecessary words like articles when possible

ORIGINAL DESCRIPTION ({current_tokens} tokens):
{description}

TASK: Rewrite this description to be CLIP-optimized and ≤ 77 tokens. Focus on the most visually important elements.

OPTIMIZED DESCRIPTION:"""
        else:
            # Subsequent attempts: more aggressive compression
            return f"""The following description is still too long for CLIP (>{self.max_tokens} tokens). Make it much more concise.

REQUIREMENTS:
- Must be ≤ 77 tokens (critical limit)
- Keep only the most essential visual elements
- Use shortest possible phrases
- Remove all unnecessary words
- Focus on key objects and their main attributes

CURRENT DESCRIPTION ({current_tokens} tokens):
{description}

TASK: Compress this to ≤ 77 tokens while keeping core visual information.

COMPRESSED DESCRIPTION:"""

    def optimize_description(self, description: str) -> Tuple[str, bool, int]:
        """
        Optimize a single description for CLIP compatibility.
        
        Returns:
            Tuple of (optimized_description, success, final_token_count)
        """
        original_tokens = self.count_tokens(description)
        
        # If already within limit, return as-is
        if original_tokens <= self.max_tokens:
            return description, True, original_tokens
        
        for attempt in range(self.max_retries):
            try:
                self.stats['api_calls'] += 1

                prompt = self.create_optimization_prompt(description, self.count_tokens(description), attempt + 1)

                # Generate optimized description
                response = self.model.generate_content(prompt)
                optimized = response.text.strip()

                # Clean up the response (remove quotes, extra whitespace)
                optimized = re.sub(r'^["\']|["\']$', '', optimized)
                optimized = re.sub(r'\s+', ' ', optimized).strip()

                # Additional cleanup for common issues
                optimized = re.sub(r'^(OPTIMIZED DESCRIPTION:|COMPRESSED DESCRIPTION:)\s*', '', optimized, flags=re.IGNORECASE)
                optimized = re.sub(r'\s*\([^)]*tokens?\)', '', optimized)  # Remove token count mentions

                # Count tokens in optimized description
                optimized_tokens = self.count_tokens(optimized)

                # Check if within token limit
                if optimized_tokens <= self.max_tokens:
                    self.stats['token_savings'] += (original_tokens - optimized_tokens)
                    return optimized, True, optimized_tokens
                else:
                    print(f"   ⚠️  Attempt {attempt + 1}: Still {optimized_tokens} tokens (>{self.max_tokens})")
                    if attempt < self.max_retries - 1:
                        self.stats['retries'] += 1
                        time.sleep(self.retry_delay)
                        # For retry, use the optimized version as input to further compress
                        description = optimized
                    
            except Exception as e:
                print(f"   ❌ Attempt {attempt + 1} failed: {e}")
                if attempt < self.max_retries - 1:
                    self.stats['retries'] += 1
                    time.sleep(self.retry_delay)
                else:
                    return description, False, original_tokens
        
        # If all retries failed, return original
        return description, False, original_tokens

    def process_class_descriptions(self, class_name: str, descriptions: List[str]) -> List[str]:
        """Process all descriptions for a single class."""
        print(f"\n🎯 Processing {class_name}...")
        print(f"   📊 {len(descriptions)} descriptions to optimize")
        
        optimized_descriptions = []
        class_stats = {'optimized': 0, 'failed': 0, 'total_tokens_saved': 0}
        
        for i, description in enumerate(descriptions, 1):
            original_tokens = self.count_tokens(description)
            
            print(f"   📝 {i}/{len(descriptions)}: {original_tokens} tokens → ", end="", flush=True)
            
            optimized, success, final_tokens = self.optimize_description(description)
            
            if success:
                if final_tokens < original_tokens:
                    print(f"{final_tokens} tokens ✅ (-{original_tokens - final_tokens})")
                    class_stats['optimized'] += 1
                    class_stats['total_tokens_saved'] += (original_tokens - final_tokens)
                else:
                    print(f"{final_tokens} tokens ✅ (no change)")
                    class_stats['optimized'] += 1
            else:
                print(f"{final_tokens} tokens ❌ (failed)")
                class_stats['failed'] += 1
            
            optimized_descriptions.append(optimized)
            self.stats['total_descriptions'] += 1
            
            # Small delay to avoid rate limiting
            time.sleep(0.1)
        
        self.stats['optimized_descriptions'] += class_stats['optimized']
        self.stats['failed_descriptions'] += class_stats['failed']
        
        print(f"   ✅ Class complete: {class_stats['optimized']} optimized, {class_stats['failed']} failed")
        print(f"   💰 Tokens saved: {class_stats['total_tokens_saved']}")
        
        return optimized_descriptions

    def load_input_file(self, input_path: str) -> Dict:
        """Load the input JSON file."""
        print(f"\n📂 Loading input file: {input_path}")
        
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Validate structure
            if 'classes' not in data:
                raise ValueError("Input file must contain 'classes' key")
            
            total_descriptions = sum(len(class_data.get('descriptions', [])) 
                                   for class_data in data['classes'].values())
            
            print(f"   ✅ Loaded successfully")
            print(f"   📊 Found {len(data['classes'])} classes")
            print(f"   📝 Total descriptions: {total_descriptions}")
            
            return data
            
        except Exception as e:
            print(f"   ❌ Error loading file: {e}")
            raise

    def save_output_file(self, data: Dict, output_path: str) -> None:
        """Save the optimized data to output file."""
        print(f"\n💾 Saving optimized descriptions to: {output_path}")
        
        try:
            # Add optimization metadata
            if 'processing_info' not in data:
                data['processing_info'] = {}
            
            data['processing_info']['clip_optimization'] = {
                'timestamp': datetime.now().isoformat(),
                'max_tokens_limit': self.max_tokens,
                'statistics': self.stats.copy(),
                'model_used': 'gemini-2.5-flash-lite',
                'optimization_completed': True
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            print(f"   ✅ Saved successfully")
            
        except Exception as e:
            print(f"   ❌ Error saving file: {e}")
            raise

    def print_final_statistics(self) -> None:
        """Print final processing statistics."""
        print(f"\n🎉 CLIP OPTIMIZATION COMPLETED!")
        print(f"=" * 50)
        print(f"📊 Processing Statistics:")
        print(f"   Total descriptions: {self.stats['total_descriptions']}")
        print(f"   Successfully optimized: {self.stats['optimized_descriptions']}")
        print(f"   Failed optimizations: {self.stats['failed_descriptions']}")
        print(f"   Success rate: {(self.stats['optimized_descriptions']/self.stats['total_descriptions']*100):.1f}%")
        print(f"   Total tokens saved: {self.stats['token_savings']}")
        print(f"   API calls made: {self.stats['api_calls']}")
        print(f"   Retries needed: {self.stats['retries']}")
        print(f"   Processing time: {self.stats['processing_time']:.1f} seconds")

    def optimize_dataset(self, input_path: str, output_path: Optional[str] = None) -> str:
        """
        Main method to optimize an entire dataset.
        
        Args:
            input_path: Path to input JSON file
            output_path: Path for output file (auto-generated if None)
            
        Returns:
            Path to the output file
        """
        start_time = time.time()
        
        # Load input data
        data = self.load_input_file(input_path)
        
        # Generate output path if not provided
        if output_path is None:
            input_file = Path(input_path)
            output_path = input_file.parent / f"{input_file.stem}_clip_optimized.json"
        
        print(f"\n🔄 Starting CLIP optimization process...")
        
        # Process each class
        for class_name, class_data in data['classes'].items():
            if 'descriptions' in class_data:
                optimized_descriptions = self.process_class_descriptions(
                    class_name, class_data['descriptions']
                )
                # Update the data with optimized descriptions
                data['classes'][class_name]['descriptions'] = optimized_descriptions
        
        # Calculate processing time
        self.stats['processing_time'] = time.time() - start_time
        
        # Save optimized data
        self.save_output_file(data, output_path)
        
        # Print final statistics
        self.print_final_statistics()
        
        return str(output_path)

    def validate_optimization(self, output_path: str) -> Dict:
        """Validate that all descriptions meet CLIP requirements."""
        print(f"\n🔍 Validating optimization results...")

        try:
            with open(output_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            validation_stats = {
                'total_descriptions': 0,
                'valid_descriptions': 0,
                'invalid_descriptions': 0,
                'max_tokens_found': 0,
                'invalid_examples': []
            }

            for class_name, class_data in data['classes'].items():
                for desc in class_data.get('descriptions', []):
                    tokens = self.count_tokens(desc)
                    validation_stats['total_descriptions'] += 1
                    validation_stats['max_tokens_found'] = max(validation_stats['max_tokens_found'], tokens)

                    if tokens <= self.max_tokens:
                        validation_stats['valid_descriptions'] += 1
                    else:
                        validation_stats['invalid_descriptions'] += 1
                        if len(validation_stats['invalid_examples']) < 5:  # Keep first 5 examples
                            validation_stats['invalid_examples'].append({
                                'class': class_name,
                                'tokens': tokens,
                                'description': desc[:100] + "..." if len(desc) > 100 else desc
                            })

            # Print validation results
            success_rate = (validation_stats['valid_descriptions'] / validation_stats['total_descriptions'] * 100)
            print(f"   📊 Validation Results:")
            print(f"      Total descriptions: {validation_stats['total_descriptions']}")
            print(f"      Valid (≤{self.max_tokens} tokens): {validation_stats['valid_descriptions']}")
            print(f"      Invalid (>{self.max_tokens} tokens): {validation_stats['invalid_descriptions']}")
            print(f"      Success rate: {success_rate:.1f}%")
            print(f"      Max tokens found: {validation_stats['max_tokens_found']}")

            if validation_stats['invalid_examples']:
                print(f"   ⚠️  Examples of invalid descriptions:")
                for example in validation_stats['invalid_examples']:
                    print(f"      {example['class']}: {example['tokens']} tokens - {example['description']}")

            if validation_stats['invalid_descriptions'] == 0:
                print(f"   ✅ All descriptions are CLIP-compatible!")
            else:
                print(f"   ⚠️  {validation_stats['invalid_descriptions']} descriptions still exceed token limit")

            return validation_stats

        except Exception as e:
            print(f"   ❌ Validation failed: {e}")
            return {}

def main():
    """Main execution function."""
    # Configuration
    PROJECT_ID = "your-project-id"  # Replace with your Google Cloud project ID
    INPUT_FILE = "extracted_ccso_dataset/imagenette_ccso_enhanced_robust_20250821_135956.json"
    
    print("🔍 CLIP Description Optimizer")
    print("=" * 40)
    print("This script optimizes image descriptions for CLIP compatibility")
    print("using Google Vertex AI Gemini 2.5 Flash Lite model.")
    print()
    
    # Check if input file exists
    if not Path(INPUT_FILE).exists():
        print(f"❌ Input file not found: {INPUT_FILE}")
        return
    
    # Get project ID from environment or user input
    project_id = os.getenv('GOOGLE_CLOUD_PROJECT', PROJECT_ID)
    if project_id == "your-project-id":
        project_id = input("Enter your Google Cloud Project ID: ").strip()
        if not project_id:
            print("❌ Project ID is required")
            return
    
    try:
        # Initialize optimizer
        optimizer = CLIPDescriptionOptimizer(project_id)
        
        # Run optimization
        output_file = optimizer.optimize_dataset(INPUT_FILE)

        # Validate results
        validation_results = optimizer.validate_optimization(output_file)

        print(f"\n✅ Optimization complete!")
        print(f"📁 Output file: {output_file}")

        if validation_results.get('invalid_descriptions', 0) == 0:
            print(f"💡 All descriptions are now ≤ 77 tokens and CLIP-optimized!")
        else:
            print(f"⚠️  {validation_results.get('invalid_descriptions', 0)} descriptions still need manual review")
            print(f"💡 {validation_results.get('valid_descriptions', 0)} descriptions are CLIP-ready!")
        
    except Exception as e:
        print(f"\n❌ Error during optimization: {e}")
        print("Please check your Google Cloud credentials and project configuration.")

if __name__ == "__main__":
    main()
