#!/usr/bin/env python3
"""
True Duplicate Remover
Finds and removes actual duplicate descriptions (identical text) while preserving all unique descriptions.
"""

import json
import shutil
from pathlib import Path
from collections import defaultdict
from datetime import datetime
import hashlib

def hash_description(description):
    """Create a hash of the description text for duplicate detection."""
    # Normalize the text: strip whitespace, convert to lowercase
    normalized = description.strip().lower()
    return hashlib.md5(normalized.encode('utf-8')).hexdigest()

def find_true_duplicates(data):
    """Find entries with identical description text."""
    print("🔍 Analyzing for true duplicates (identical text)...")
    
    # Group by description hash
    description_groups = defaultdict(list)
    for i, entry in enumerate(data):
        if 'description' in entry and entry['description']:
            desc_hash = hash_description(entry['description'])
            description_groups[desc_hash].append((i, entry))
    
    # Find duplicates
    duplicates_found = []
    unique_descriptions = 0
    total_duplicates = 0
    
    for desc_hash, entries in description_groups.items():
        if len(entries) > 1:
            # This description appears multiple times
            duplicates_found.append({
                'hash': desc_hash,
                'description_preview': entries[0][1]['description'][:100] + "...",
                'count': len(entries),
                'entries': entries
            })
            total_duplicates += len(entries) - 1  # All but one are duplicates
        unique_descriptions += 1
    
    print(f"📊 Duplicate Analysis Results:")
    print(f"   Total entries: {len(data)}")
    print(f"   Unique descriptions: {unique_descriptions}")
    print(f"   Duplicate groups found: {len(duplicates_found)}")
    print(f"   Total duplicate entries: {total_duplicates}")
    
    if duplicates_found:
        print(f"\n🔍 Sample duplicate groups:")
        for i, dup_group in enumerate(duplicates_found[:5]):  # Show first 5
            print(f"   Group {i+1}: {dup_group['count']} copies of:")
            print(f"      \"{dup_group['description_preview']}\"")
            
            # Show which images have this duplicate
            images = set()
            for _, entry in dup_group['entries']:
                if 'image_filename' in entry:
                    images.add(entry['image_filename'])
            print(f"      Found in {len(images)} images: {list(images)[:3]}{'...' if len(images) > 3 else ''}")
    
    return duplicates_found

def remove_true_duplicates(data):
    """Remove true duplicates, keeping only one copy of each unique description."""
    print("🧹 Removing true duplicates...")
    
    # Track which descriptions we've seen
    seen_descriptions = set()
    unique_entries = []
    removed_count = 0
    
    # Group entries by image for better reporting
    image_stats = defaultdict(lambda: {'original': 0, 'kept': 0, 'removed': 0})
    
    for entry in data:
        if 'description' in entry and entry['description']:
            desc_hash = hash_description(entry['description'])
            image_filename = entry.get('image_filename', 'unknown')
            image_stats[image_filename]['original'] += 1
            
            if desc_hash not in seen_descriptions:
                # First time seeing this description - keep it
                seen_descriptions.add(desc_hash)
                unique_entries.append(entry)
                image_stats[image_filename]['kept'] += 1
            else:
                # Duplicate description - remove it
                removed_count += 1
                image_stats[image_filename]['removed'] += 1
                print(f"   🗑️  Removed duplicate from {image_filename}")
        else:
            # Entry without description - keep it
            unique_entries.append(entry)
    
    print(f"\n✅ True duplicate removal completed:")
    print(f"   Original entries: {len(data)}")
    print(f"   Unique entries kept: {len(unique_entries)}")
    print(f"   Duplicates removed: {removed_count}")
    
    # Show per-image statistics for images that had duplicates
    images_with_duplicates = {img: stats for img, stats in image_stats.items() if stats['removed'] > 0}
    if images_with_duplicates:
        print(f"\n📊 Images that had duplicates removed:")
        for img, stats in list(images_with_duplicates.items())[:10]:  # Show first 10
            print(f"   {img}: {stats['original']} → {stats['kept']} (removed {stats['removed']})")
        if len(images_with_duplicates) > 10:
            print(f"   ... and {len(images_with_duplicates) - 10} more images")
    
    return unique_entries

def analyze_image_distribution(data):
    """Analyze how many descriptions each image has after duplicate removal."""
    print("\n📊 Analyzing description distribution per image...")
    
    image_counts = defaultdict(int)
    for entry in data:
        if 'image_filename' in entry:
            image_counts[entry['image_filename']] += 1
    
    # Count distribution
    distribution = defaultdict(int)
    for count in image_counts.values():
        distribution[count] += 1
    
    print(f"   Distribution of descriptions per image:")
    for desc_count in sorted(distribution.keys()):
        print(f"     {desc_count} descriptions: {distribution[desc_count]} images")
    
    return image_counts

def remove_duplicates_from_file(input_file_path, create_backup=True):
    """Remove true duplicates from a batch file."""
    input_path = Path(input_file_path)
    
    if not input_path.exists():
        print(f"❌ File not found: {input_path}")
        return None
    
    print(f"🚀 Removing true duplicates from: {input_path.name}")
    print("=" * 60)
    
    # Create backup if requested
    if create_backup:
        backup_path = input_path.parent / f"{input_path.stem}_backup_before_dedup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        shutil.copy2(input_path, backup_path)
        print(f"💾 Created backup: {backup_path.name}")
    
    # Load the data
    try:
        with open(input_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        print(f"✅ Loaded {len(data)} entries")
    except Exception as e:
        print(f"❌ Error loading file: {e}")
        return None
    
    # Find duplicates
    duplicates_found = find_true_duplicates(data)
    
    if not duplicates_found:
        print("✅ No true duplicates found! File is already clean.")
        return input_path
    
    # Remove duplicates
    cleaned_data = remove_true_duplicates(data)
    
    # Analyze final distribution
    analyze_image_distribution(cleaned_data)
    
    # Create deduplicated file
    dedup_path = input_path.parent / f"{input_path.stem}_deduplicated.json"
    
    try:
        with open(dedup_path, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, indent=2, ensure_ascii=False)
        print(f"\n✅ Deduplicated file saved: {dedup_path.name}")
        
        # Verify the deduplicated file
        print(f"\n🔍 Verification:")
        with open(dedup_path, 'r', encoding='utf-8') as f:
            verification_data = json.load(f)
        
        verify_duplicates = find_true_duplicates(verification_data)
        
        if not verify_duplicates:
            print("✅ Verification passed: No true duplicates in deduplicated file")
        else:
            print(f"⚠️  Verification warning: Still {len(verify_duplicates)} duplicate groups found")
        
        return dedup_path
        
    except Exception as e:
        print(f"❌ Error saving deduplicated file: {e}")
        return None

def main():
    """Main execution."""
    # Target the problematic tench batch file
    batch_file = "raw_results/batch_0048_tench_results.json"
    
    print("🔍 True Duplicate Remover")
    print("=" * 40)
    print("This script removes entries with identical description text,")
    print("keeping only one copy of each unique description.")
    print()
    
    dedup_file = remove_duplicates_from_file(batch_file, create_backup=True)
    
    if dedup_file:
        print(f"\n🎉 SUCCESS!")
        print(f"📁 Original file: {batch_file}")
        print(f"📁 Deduplicated file: {dedup_file.name}")
        print(f"📁 Backup created with timestamp")
        print(f"\n💡 The original file is preserved. The deduplicated file contains only unique descriptions.")
    else:
        print(f"\n❌ FAILED to remove duplicates from the batch file")

if __name__ == "__main__":
    main()
