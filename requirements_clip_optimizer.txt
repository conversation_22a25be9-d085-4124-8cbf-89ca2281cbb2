# Requirements for CLIP Description Optimizer

# Core dependencies
google-cloud-aiplatform>=1.38.0
vertexai>=1.38.0
tiktoken>=0.5.0

# Optional: For testing and examples
# Uncomment the following lines if you want to run the example scripts:

# For example_clip_usage.py
# torch>=1.9.0
# torchvision>=0.10.0
# Pillow>=8.0.0
# git+https://github.com/openai/CLIP.git

# For enhanced token counting (alternative to tiktoken)
# transformers>=4.30.0

# Standard libraries (included with Python)
# json, os, time, re, pathlib, datetime, typing are built-in
