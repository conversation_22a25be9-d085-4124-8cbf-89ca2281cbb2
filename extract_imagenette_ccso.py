#!/usr/bin/env python3
"""
Robust ImageNette CCSO Dataset Extractor with JSON Error Handling
Handles corrupted batch files and extracts valid descriptions
"""

import json
import os
import re
from pathlib import Path
from collections import defaultdict
from datetime import datetime

class RobustImageNetteCCSOExtractor:
    def __init__(self, base_path):
        self.base_path = Path(base_path)
        self.raw_results_dir = self.base_path / "raw_results"
        self.output_dir = self.base_path / "extracted_ccso_dataset"
        self.backup_dir = self.base_path / "corrupted_backups"
        
        # Create directories
        self.output_dir.mkdir(exist_ok=True)
        self.backup_dir.mkdir(exist_ok=True)
        
        self.corruption_log = []
        
        print(f"🔍 Initialized robust extractor for: {self.base_path}")

    def backup_corrupted_file(self, file_path):
        """Create backup of corrupted file before fixing."""
        backup_path = self.backup_dir / f"{file_path.stem}_corrupted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(file_path, 'r', encoding='utf-8') as src:
                content = src.read()
            with open(backup_path, 'w', encoding='utf-8') as dst:
                dst.write(content)
            print(f"   💾 Backed up to: {backup_path.name}")
            return backup_path
        except Exception as e:
            print(f"   ❌ Backup failed: {e}")
            return None

    def fix_json_corruption(self, file_path):
        """Fix common JSON corruption patterns in batch files."""
        print(f"🔧 Attempting to fix JSON corruption in {file_path.name}...")
        
        # Backup first
        backup_path = self.backup_corrupted_file(file_path)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            fixed = False
            
            # Fix 1: Remove the corrupted pattern "} ]t/imagenette..." 
            # This looks like a truncated "image_path" field that got corrupted
            corruption_pattern = r'}\s*\]t/imagenette[^{]*?(?=\s*{|\s*$)'
            if re.search(corruption_pattern, content):
                content = re.sub(corruption_pattern, '', content)
                fixed = True
                print(f"   ✅ Removed corrupted truncated entries")
            
            # Fix 2: Handle missing opening braces and fields
            # Pattern: missing opening { and required fields
            missing_opening_pattern = r'(\s*)"image_path":\s*"/[^"]*"'
            matches = re.finditer(missing_opening_pattern, content)
            
            for match in reversed(list(matches)):  # Process in reverse to maintain positions
                # Check if this entry is missing opening brace
                start_pos = match.start()
                
                # Look backwards to see if there's a proper opening
                preceding_text = content[max(0, start_pos-100):start_pos]
                
                # If we don't find proper JSON structure, this entry is corrupted
                if not re.search(r'{\s*"success":\s*(true|false)', preceding_text):
                    # Remove this corrupted entry
                    end_pos = match.end()
                    
                    # Find where this corrupted entry ends (look for next { or end of file)
                    remaining_content = content[end_pos:]
                    next_entry_match = re.search(r'\s*,?\s*{', remaining_content)
                    
                    if next_entry_match:
                        end_pos += next_entry_match.start()
                    
                    # Remove the corrupted entry
                    content = content[:start_pos] + content[end_pos:]
                    fixed = True
                    print(f"   ✅ Removed corrupted entry missing opening fields")
            
            # Fix 3: Clean up JSON structure issues
            # Remove any trailing commas before closing bracket
            content = re.sub(r',(\s*\])', r'\1', content)
            
            # Fix 4: Ensure proper array structure
            content = content.strip()
            if not content.startswith('['):
                content = '[' + content
            if not content.endswith(']'):
                content = content + ']'
            
            # Try to parse the fixed JSON
            try:
                json.loads(content)
                print(f"   ✅ JSON structure validated after fixes")
            except json.JSONDecodeError as e:
                print(f"   ⚠️  JSON still invalid after fixes: {e}")
                # Try more aggressive cleaning
                content = self.aggressive_json_cleaning(content)
            
            if fixed and content != original_content:
                # Write the fixed content back
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.corruption_log.append({
                    'file': file_path.name,
                    'backup': backup_path.name if backup_path else None,
                    'fixes_applied': True,
                    'timestamp': datetime.now().isoformat()
                })
                
                print(f"   ✅ File fixed and saved")
                return True
            else:
                print(f"   ℹ️  No fixes needed")
                return False
                
        except Exception as e:
            print(f"   ❌ Error during fix: {e}")
            self.corruption_log.append({
                'file': file_path.name,
                'error': str(e),
                'fixes_applied': False,
                'timestamp': datetime.now().isoformat()
            })
            return False

    def aggressive_json_cleaning(self, content):
        """More aggressive JSON cleaning for severely corrupted files."""
        print(f"   🔧 Applying aggressive cleaning...")
        
        # Extract valid JSON objects
        valid_objects = []
        
        # Find all complete JSON objects with required fields
        object_pattern = r'{\s*"success":\s*(true|false).*?(?="prompt_variation":\s*\d+.*?"session_id":\s*"[^"]*"\s*}'
        
        for match in re.finditer(object_pattern, content, re.DOTALL):
            obj_text = match.group(0) + '}'
            try:
                obj = json.loads(obj_text)
                if obj.get('success') and 'description' in obj and 'object_name' in obj:
                    valid_objects.append(obj)
            except:
                continue
        
        print(f"   ✅ Extracted {len(valid_objects)} valid objects")
        
        # Reconstruct clean JSON
        return json.dumps(valid_objects, indent=2)

    def safe_load_batch_file(self, file_path):
        """Safely load batch file with corruption handling."""
        print(f"📖 Loading {file_path.name}...")
        
        try:
            # First, try normal loading
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"   ✅ Loaded successfully")
            return data
            
        except json.JSONDecodeError as e:
            print(f"   ⚠️  JSON corruption detected: {e}")
            
            # Try to fix the corruption
            if self.fix_json_corruption(file_path):
                # Retry loading after fix
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"   ✅ Loaded successfully after fix")
                    return data
                except json.JSONDecodeError as e2:
                    print(f"   ❌ Still corrupted after fix: {e2}")
            
            # If fixing failed, try to extract what we can
            return self.extract_partial_data(file_path)
        
        except Exception as e:
            print(f"   ❌ Unexpected error: {e}")
            return []

    def extract_partial_data(self, file_path):
        """Extract partial data from severely corrupted files."""
        print(f"   🔄 Attempting partial data extraction...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract descriptions using regex
            descriptions = []
            
            # Pattern to find description fields
            desc_pattern = r'"description":\s*"([^"]*(?:\\.[^"]*)*)"'
            object_pattern = r'"object_name":\s*"([^"]*)"'
            
            desc_matches = re.findall(desc_pattern, content)
            obj_matches = re.findall(object_pattern, content)
            
            # Try to pair descriptions with object names
            for i, desc in enumerate(desc_matches):
                obj_name = obj_matches[i] if i < len(obj_matches) else 'unknown'
                
                descriptions.append({
                    'success': True,
                    'description': desc.replace('\\"', '"'),  # Unescape quotes
                    'object_name': obj_name,
                    'prompt_variation': (i % 3) + 1,  # Estimate variation
                    'partial_extraction': True
                })
            
            print(f"   ⚡ Extracted {len(descriptions)} descriptions via regex")
            return descriptions
            
        except Exception as e:
            print(f"   ❌ Partial extraction failed: {e}")
            return []

    def find_batch_files(self):
        """Find all batch files and organize by class."""
        print("\n🔍 Scanning batch files...")
        
        batch_files = list(self.raw_results_dir.glob("batch_*.json"))
        print(f"Found {len(batch_files)} batch files")
        
        # Group by class
        class_batches = defaultdict(list)
        
        for batch_file in batch_files:
            # Extract class name from filename
            match = re.search(r'batch_(\d+)_(.+)_results\.json', batch_file.name)
            if match:
                batch_num = int(match.group(1))
                class_name = match.group(2)
                class_batches[class_name].append((batch_num, batch_file))
        
        # Sort and get last batch for each class
        last_batches = {}
        for class_name, batches in class_batches.items():
            batches.sort(key=lambda x: x[0])
            last_batch_num, last_batch_file = batches[-1]
            last_batches[class_name] = {
                'file': last_batch_file,
                'batch_num': last_batch_num,
                'total_batches': len(batches)
            }
        
        print(f"\n📊 Classes and their last batches:")
        for class_name, info in last_batches.items():
            print(f"   {class_name}: batch_{info['batch_num']:04d}")
        
        return last_batches

    def extract_descriptions_from_batch(self, batch_file):
        """Extract descriptions with robust error handling."""
        batch_data = self.safe_load_batch_file(batch_file)
        
        descriptions = []
        successful_count = 0
        failed_count = 0
        partial_count = 0
        
        for entry in batch_data:
            if entry.get('success', False) and 'description' in entry:
                descriptions.append({
                    'description': entry['description'],
                    'prompt_variation': entry.get('prompt_variation', 1),
                    'image_filename': entry.get('image_filename', ''),
                    'tokens': entry.get('output_tokens', 0),
                    'cost': entry.get('cost', 0.0),
                    'partial': entry.get('partial_extraction', False)
                })
                
                if entry.get('partial_extraction', False):
                    partial_count += 1
                else:
                    successful_count += 1
            else:
                failed_count += 1
        
        print(f"   ✅ {successful_count} complete, ⚡ {partial_count} partial, ❌ {failed_count} failed")
        return descriptions

    def organize_by_class(self, last_batches):
        """Extract and organize descriptions by class."""
        print(f"\n🗂️  Extracting descriptions by class...")
        
        class_descriptions = {}
        processing_stats = {
            'classes_processed': 0,
            'total_descriptions': 0,
            'total_images': 0,
            'total_cost': 0.0,
            'corrupted_files': len(self.corruption_log),
            'extraction_timestamp': datetime.now().isoformat()
        }
        
        for class_name, batch_info in last_batches.items():
            print(f"\n🎯 Processing {class_name}...")
            
            descriptions = self.extract_descriptions_from_batch(batch_info['file'])
            
            if descriptions:
                images_set = set()
                class_cost = 0.0
                description_texts = []
                
                for desc in descriptions:
                    description_texts.append(desc['description'])
                    images_set.add(desc['image_filename'])
                    class_cost += desc['cost']
                
                class_descriptions[class_name] = {
                    'descriptions': description_texts,
                    'metadata': {
                        'total_descriptions': len(description_texts),
                        'unique_images': len(images_set),
                        'descriptions_per_image': len(description_texts) / len(images_set) if images_set else 0,
                        'total_cost': class_cost,
                        'avg_tokens': sum(d['tokens'] for d in descriptions) / len(descriptions) if descriptions else 0,
                        'has_partial_extractions': any(d.get('partial', False) for d in descriptions)
                    }
                }
                
                processing_stats['total_descriptions'] += len(description_texts)
                processing_stats['total_images'] += len(images_set)
                processing_stats['total_cost'] += class_cost
                processing_stats['classes_processed'] += 1
                
                print(f"   📝 {len(description_texts)} descriptions from {len(images_set)} images")
            else:
                print(f"   ❌ No valid descriptions found for {class_name}")
        
        return class_descriptions, processing_stats

    def save_corruption_log(self):
        """Save corruption handling log."""
        if self.corruption_log:
            log_file = self.output_dir / f"corruption_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(self.corruption_log, f, indent=2)
            print(f"📋 Corruption log saved: {log_file.name}")

    def create_ccso_datasets(self, class_descriptions, processing_stats):
        """Create CCSO datasets."""
        print(f"\n📦 Creating CCSO datasets...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Simple CCSO format
        ccso_simple = {}
        for class_name, data in class_descriptions.items():
            ccso_simple[class_name] = data['descriptions']
        
        simple_file = self.output_dir / f"imagenette_ccso_robust_{timestamp}.json"
        with open(simple_file, 'w', encoding='utf-8') as f:
            json.dump(ccso_simple, f, indent=2, ensure_ascii=False)
        
        # Enhanced format with corruption info
        enhanced_file = self.output_dir / f"imagenette_ccso_enhanced_robust_{timestamp}.json"
        enhanced_data = {
            'classes': class_descriptions,
            'processing_info': processing_stats,
            'corruption_handling': {
                'corrupted_files_fixed': len(self.corruption_log),
                'corruption_log': self.corruption_log
            }
        }
        
        with open(enhanced_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Robust CCSO dataset: {simple_file}")
        print(f"✅ Enhanced dataset: {enhanced_file}")
        
        return {'simple': simple_file, 'enhanced': enhanced_file}

    def extract_complete_dataset(self):
        """Complete robust extraction pipeline."""
        print("🚀 Starting Robust ImageNette CCSO Dataset Extraction")
        print("=" * 60)
        
        # Find and process batch files
        last_batches = self.find_batch_files()
        
        if not last_batches:
            print("❌ No batch files found!")
            return None
        
        # Extract with corruption handling
        class_descriptions, processing_stats = self.organize_by_class(last_batches)
        
        # Save corruption log
        self.save_corruption_log()
        
        if not class_descriptions:
            print("❌ No descriptions extracted!")
            return None
        
        # Create datasets
        output_files = self.create_ccso_datasets(class_descriptions, processing_stats)
        
        # Print summary
        print(f"\n🎉 ROBUST EXTRACTION COMPLETED!")
        print(f"📊 Classes extracted: {len(class_descriptions)}")
        print(f"📝 Total descriptions: {processing_stats['total_descriptions']}")
        print(f"🔧 Files fixed: {processing_stats['corrupted_files']}")
        print(f"💰 Total cost: ${processing_stats['total_cost']:.4f}")
        
        return output_files

def main():
    """Main execution."""
    # Update path for your system
    BASE_PATH = "/content/drive/MyDrive/ImageNette_CCSO_Dataset"  # Update this path
    
    extractor = RobustImageNetteCCSOExtractor(BASE_PATH)
    output_files = extractor.extract_complete_dataset()
    
    if output_files:
        print(f"\n✅ Dataset ready for CCSO clustering!")
        print(f"📁 Primary file: {output_files['simple'].name}")

if __name__ == "__main__":
    main()